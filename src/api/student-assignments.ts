import { apiClient } from '@/lib/api-client';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';
// Timestamp handling now done with regular Date objects

export interface AssignmentSubmission {
  id?: string;
  assignment_id: string;
  student_id: string;
  content?: string;
  file_url?: string;
  file_name?: string;
  file_id?: string; // Google Drive file ID
  submitted_at: string;
  status: 'submitted' | 'graded' | 'late';
  grade?: number;
  feedback?: string;
  graded_at?: string;
  graded_by?: string;
}

// Base assignment interface
export interface Assignment {
  id: string;
  title: string;
  description: string;
  due_date: string;
  level_id: string;
  created_at: string;
  updated_at: string;
}

// Base submission interface
export interface Submission {
  id: string;
  assignment_id: string;
  student_id: string;
  content?: string;
  file_url?: string;
  submitted_at: string;
  grade?: number;
  feedback?: string;
  graded_at?: string;
  graded_by?: string;
}

export interface EnhancedAssignment extends Assignment {
  status: 'pending' | 'in_progress' | 'completed' | 'late' | 'submitted' | 'graded';
  progress: number;
  submission: Submission | null;
  attachments?: {
    id: string;
    name: string;
    size: string;
    type: string;
    url: string;
  }[];
}

// Get assignments for a student based on their level
export const getStudentAssignments = async (studentId: string, levelId: string): Promise<EnhancedAssignment[]> => {
  try {
    console.log(`Fetching assignments for student: ${studentId} in level: ${levelId}`);
    
    if (!levelId) {
      throw new Error('Student is not assigned to any level');
    }
    
    // Get assignments for the student's level
    const assignments = await apiClient.get<Assignment[]>(`/levels/${levelId}/assignments`);

    if (!assignments.length) {
      console.log('No assignments found for level:', levelId);
      return [];
    }

    // Get submissions for this student to determine status
    const submissions = await apiClient.get<Submission[]>(`/students/${studentId}/submissions`);

    // Map submissions by assignment ID for quick lookup
    const submissionMap = submissions.reduce((map: Record<string, Submission>, submission: Submission) => {
      map[submission.assignment_id] = submission;
      return map;
    }, {} as Record<string, Submission>);
    
    // Enhance assignments with submission status
    const enhancedAssignments = assignments.map((assignment: Assignment) => {
      const submission = submissionMap[assignment.id];
      const dueDate = new Date(assignment.due_date);
      const now = new Date();

      let status: EnhancedAssignment['status'] = 'pending';
      let progress = 0;

      if (submission) {
        if (submission.grade !== undefined && submission.grade !== null) {
          status = 'graded';
        } else {
          status = 'submitted';
        }
        progress = 100;
      } else if (dueDate < now) {
        status = 'late';
        progress = 0;
      } else {
        status = 'pending';
        progress = 0;
      }
      
      return {
        ...assignment,
        status,
        progress,
        submission: submission || null,
        attachments: [] // Initialize with empty array
      } as EnhancedAssignment;
    });
    
    // Sort assignments by due date (closest first)
    return enhancedAssignments.sort((a, b) => 
      new Date(a.due_date).getTime() - new Date(b.due_date).getTime()
    );
  } catch (error) {
    console.error('Error fetching student assignments:', error);
    throw error;
  }
};

// Get a specific assignment with submission details for a student
export const getStudentAssignment = async (assignmentId: string, studentId: string): Promise<EnhancedAssignment | null> => {
  try {
    console.log(`Fetching assignment details: ${assignmentId} for student: ${studentId}`);
    
    // Get the assignment
    const assignment = await assignmentModel.getAssignmentById(assignmentId);
    
    if (!assignment) {
      throw new Error('Assignment not found');
    }
    
    // Get submission for this assignment if it exists
    const submissions = await submissionModel.getSubmissionsByAssignmentAndStudent(
      assignmentId, 
      studentId
    );
    
    const submission = submissions.length > 0 ? submissions[0] : null;
    const dueDate = new Date(assignment.due_date);
    const now = new Date();
    
    let status = 'pending';
    let progress = 0;
    
    if (submission) {
      status = submission.status;
      progress = 100;
    } else if (dueDate < now) {
      status = 'late';
    }
    
    return {
      ...assignment,
      status,
      progress,
      submission,
      attachments: [] // Initialize with empty array
    } as EnhancedAssignment;
  } catch (error) {
    console.error('Error fetching student assignment details:', error);
    throw error;
  }
};

// Submit an assignment
export const submitAssignment = async (data: Omit<AssignmentSubmission, 'id' | 'submitted_at' | 'status'>) => {
  try {
    console.log('Submitting assignment:', data);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Check if assignment exists
    const assignment = await assignmentModel.getAssignmentById(data.assignment_id);
    if (!assignment) {
      throw new Error('Assignment not found');
    }
    
    // Check if due date has passed
    const dueDate = new Date(assignment.due_date);
    const now = new Date();
    const status = dueDate < now ? 'late' : 'submitted';
    
    // Check if submission already exists
    const existingSubmissions = await submissionModel.getSubmissionsByAssignmentAndStudent(
      data.assignment_id,
      data.student_id
    );
    
    let submissionId;
    
    if (existingSubmissions.length > 0) {
      // Update existing submission
      const existingSubmission = existingSubmissions[0];
      
      // If there's a new file and an old file_id, we should delete the old file
      // This would be handled by a separate function to delete the file from Google Drive
      // if (data.file_id && existingSubmission.file_id && data.file_id !== existingSubmission.file_id) {
      //   try {
      //     await deleteFile(existingSubmission.file_id);
      //   } catch (error) {
      //     console.warn('Error deleting old file:', error);
      //     // Continue even if old file deletion fails
      //   }
      // }
      
      await submissionModel.updateSubmission(existingSubmission.id, {
        ...data,
        status,
        submitted_at: new Date().toISOString()
      });
      submissionId = existingSubmission.id;
    } else {
      // Create new submission
      submissionId = await submissionModel.createSubmission({
        ...data,
        status,
        submitted_at: new Date().toISOString()
      });
    }
    
    await logActivity('assignment_submitted', {
      assignmentId: data.assignment_id,
      studentId: data.student_id,
      submissionId
    });
    
    toast.success('Assignment submitted successfully');
    
    return {
      id: submissionId,
      ...data,
      status,
      submitted_at: new Date().toISOString()
    };
  } catch (error: any) {
    console.error('Error submitting assignment:', error);
    toast.error(error.message || 'Failed to submit assignment');
    throw error;
  }
}; 